<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE 客户端</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .connection-status {
            transition: all 0.3s ease;
        }

        .message-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .json-editor {
            font-family: 'JetBrains Mono', 'Monaco', 'Menlo', 'Ubuntu Mono', 'Courier New', monospace;
            background: linear-gradient(to right, #f8fafc 0%, #f1f5f9 100%);
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            resize: vertical;
            min-height: 200px;
        }

        .json-editor:focus {
            background: #ffffff;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .json-editor::placeholder {
            color: #94a3b8;
            font-style: italic;
        }

        .search-highlight {
            background-color: #fef08a;
            padding: 1px 2px;
            border-radius: 2px;
        }

        .search-current {
            background-color: #fb923c;
            color: white;
            padding: 1px 2px;
            border-radius: 2px;
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- 头部 -->
        <header class="text-center mb-4">
            <p class="text-gray-600">Server-Sent Events / HTTP 响应接收工具</p>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-5 gap-8">
            <!-- 右侧数据显示面板 -->
            <div class="bg-white rounded-lg shadow-lg p-6 lg:col-span-3">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center justify-between">
                    <span class="flex items-center">
                        <svg class="w-6 h-6 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                            </path>
                        </svg>
                        响应数据
                    </span>
                    <button id="clearDataIconBtn" title="清空数据"
                        class="p-1 text-gray-500 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-red-300 rounded-md transition duration-150">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                            </path>
                        </svg>
                    </button>
                </h2>

                <!-- 搜索栏 -->
                <div class="mb-4">
                    <div class="flex items-center space-x-2">
                        <div class="flex-1 relative">
                            <input type="text" id="searchInput"
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="搜索消息内容...">
                            <svg class="w-4 h-4 absolute left-3 top-3 text-gray-400" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <button id="searchPrevBtn"
                            class="px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled title="上一个">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7">
                                </path>
                            </svg>
                        </button>
                        <button id="searchNextBtn"
                            class="px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled title="下一个">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <div id="searchStatus" class="text-xs text-gray-500 mt-1"></div>
                </div>

                <!-- 数据显示区域 -->
                <div id="messagesContainer"
                    class="h-[28rem] overflow-y-auto border border-gray-200 rounded-md p-4 bg-gray-50">
                    <div class="text-center text-gray-500 py-8">
                        <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4">
                            </path>
                        </svg>
                        <p>等待接收数据...</p>
                        <p class="text-sm">支持 SSE 流式响应和普通 HTTP 响应</p>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="mt-4 grid grid-cols-3 gap-4 text-center">
                    <div class="bg-blue-50 p-2 rounded-md">
                        <div id="totalMessages" class="text-xl font-bold text-blue-600">0</div>
                        <div class="text-sm text-blue-500">总消息数</div>
                    </div>
                    <div class="bg-green-50 p-2 rounded-md">
                        <div id="connectionTime" class="text-xl font-bold text-green-600">00:00</div>
                        <div class="text-sm text-green-500">连接时长</div>
                    </div>
                    <div class="bg-purple-50 p-2 rounded-md">
                        <div id="lastMessageTime" class="text-xl font-bold text-purple-600">--:--</div>
                        <div class="text-sm text-purple-500">最后消息</div>
                    </div>
                </div>
            </div>

            <!-- 左侧配置面板 -->
            <div class="bg-white rounded-lg shadow-lg p-6 lg:col-span-2">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                    <svg class="w-6 h-6 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                        </path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    连接配置
                </h2>

                <!-- 服务地址输入 -->
                <div class="mb-4">
                    <label for="sseUrl" class="block text-sm font-medium text-gray-700 mb-2">服务地址</label>
                    <input type="url" id="sseUrl"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="https://example.com/events" value="http://127.0.0.1:8082/v1/chat/completions">
                </div>

                <!-- POST请求体 -->
                <div class="mb-4">
                    <label for="requestData" class="block text-sm font-medium text-gray-700 mb-2">
                        POST 请求体 (JSON格式)
                        <button id="formatJsonBtn"
                            class="ml-2 px-2 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded transition duration-200">
                            格式化
                        </button>
                    </label>
                    <textarea id="requestData" rows="12"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent json-editor text-sm leading-relaxed"
                        placeholder='{"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "Hello"}], "stream": true}'>{
                        "async_task": "",
                        "async_force": true,
                        "async_task_id": "683927e81b7dd9d62d4af544",
                        "messages": [
                        {
                        "role": "user",
                        "content": "请生成一个介绍《哪吒之魔童闹海》影响的ppt"
                        },
                        {
                        "role": "assistant",
                        "content": "需要明确分析维度和目标受众以提供更有针对性的内容"
                        },
                        {
                        "role": "user",
                        "content": "问: 您希望重点分析哪些影响层面? 答: 市场影响,文化影响,社会影响,产业影响; 问: 目标受众是哪类群体? 答: 普通观众; 问: 展示风格偏好? 答: 视觉导向; 问: ppt页数? 答: 8张"
                        }
                        ]}</textarea>
                    <p class="text-xs text-gray-500 mt-1">
                        数据将作为JSON请求体发送
                    </p>
                </div>

                <!-- 连接状态和响应类型 -->
                <div class="mb-4 space-y-2">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">连接状态:</span>
                        <span id="connectionStatus"
                            class="connection-status px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-600">
                            未连接
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">响应类型:</span>
                        <span id="responseType"
                            class="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-600">
                            未知
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">滚动模式:</span>
                        <span id="scrollModeStatus"
                            class="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-600">
                            🔄 自动滚动
                        </span>
                    </div>
                </div>

                <!-- 控制按钮 -->
                <div class="flex space-x-3">
                    <button id="connectBtn"
                        class="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition duration-200 flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        发送
                    </button>
                    <button id="disconnectBtn"
                        class="flex-1 bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md transition duration-200 flex items-center justify-center"
                        disabled>
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728">
                            </path>
                        </svg>
                        断开
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let abortController = null;
        let messageCount = 0;
        let connectionStartTime = null;
        let connectionTimer = null;
        let searchMatches = [];
        let currentSearchIndex = -1;
        let lastSearchTerm = '';

        // 🎯 滚动控制变量 - 明确区分新消息滚动和搜索滚动
        let autoScrollDisabled = false; // 是否禁用新消息的自动滚动
        let isSystemScrolling = false; // 是否是系统触发的滚动（用于忽略滚动事件）

        const elements = {
            sseUrl: document.getElementById('sseUrl'),
            requestData: document.getElementById('requestData'),
            connectBtn: document.getElementById('connectBtn'),
            disconnectBtn: document.getElementById('disconnectBtn'),
            connectionStatus: document.getElementById('connectionStatus'),
            responseType: document.getElementById('responseType'),
            messagesContainer: document.getElementById('messagesContainer'),
            totalMessages: document.getElementById('totalMessages'),
            connectionTime: document.getElementById('connectionTime'),
            lastMessageTime: document.getElementById('lastMessageTime'),
            searchInput: document.getElementById('searchInput'),
            searchPrevBtn: document.getElementById('searchPrevBtn'),
            searchNextBtn: document.getElementById('searchNextBtn'),
            searchStatus: document.getElementById('searchStatus'),
            clearDataIconBtn: document.getElementById('clearDataIconBtn'),
            scrollModeStatus: document.getElementById('scrollModeStatus')
        };

        // 🎯 新消息自动滚动到底部（只受 autoScrollDisabled 控制）
        function scrollToBottom() {
            if (!autoScrollDisabled) {
                isSystemScrolling = true;
                elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
                setTimeout(() => {
                    isSystemScrolling = false;
                }, 100);
            }
        }

        // 🎯 搜索滚动（始终可用，不受 autoScrollDisabled 影响）
        function scrollToSearchResult(element) {
            isSystemScrolling = true;
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            setTimeout(() => {
                isSystemScrolling = false;
            }, 500);
        }

        // 🎯 更新滚动状态显示
        function updateScrollStatus() {
            if (autoScrollDisabled) {
                elements.scrollModeStatus.textContent = '🔒 手动模式';
                elements.scrollModeStatus.className = 'px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-600';
            } else {
                elements.scrollModeStatus.textContent = '🔄 自动滚动';
                elements.scrollModeStatus.className = 'px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-600';
            }
        }

        // 🎯 处理滚动事件 - 只禁用新消息的自动滚动，不影响搜索功能
        function handleScroll() {
            // 如果是系统触发的滚动（新消息滚动、搜索滚动等），忽略
            if (isSystemScrolling) {
                return;
            }

            // 用户手动滚动，禁用新消息的自动滚动（但不影响搜索定位）
            if (!autoScrollDisabled) {
                autoScrollDisabled = true;
                updateScrollStatus();
                console.log('用户手动滚动，已禁用新消息自动滚动（搜索定位仍然可用）');
            }
        }

        // 搜索功能
        function highlightSearchTerm(text, searchTerm) {
            if (!searchTerm.trim()) return text;
            const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
            return text.replace(regex, '<span class="search-highlight">$1</span>');
        }

        function performSearch(preserveCurrentIndex = false) {
            const searchTerm = elements.searchInput.value.trim();
            clearSearchHighlights();

            if (!searchTerm) {
                updateSearchStatus('');
                elements.searchPrevBtn.disabled = true;
                elements.searchNextBtn.disabled = true;
                searchMatches = [];
                currentSearchIndex = -1;
                lastSearchTerm = '';
                return;
            }

            const searchTermChanged = lastSearchTerm !== searchTerm;
            lastSearchTerm = searchTerm;
            searchMatches = [];

            const messages = elements.messagesContainer.querySelectorAll('.message-fade-in');
            messages.forEach((message) => {
                const content = message.textContent.toLowerCase();
                if (content.includes(searchTerm.toLowerCase())) {
                    searchMatches.push(message);

                    const preElement = message.querySelector('pre');
                    if (preElement) {
                        const originalText = preElement.getAttribute('data-original') || preElement.textContent;
                        preElement.setAttribute('data-original', originalText);
                        preElement.innerHTML = highlightSearchTerm(originalText, searchTerm);
                    }
                }
            });

            if (searchMatches.length > 0) {
                if (searchTermChanged || !preserveCurrentIndex) {
                    currentSearchIndex = 0;
                } else {
                    if (currentSearchIndex >= searchMatches.length) {
                        currentSearchIndex = searchMatches.length - 1;
                    } else if (currentSearchIndex < 0) {
                        currentSearchIndex = 0;
                    }
                }

                highlightCurrentMatch();
                elements.searchPrevBtn.disabled = false;
                elements.searchNextBtn.disabled = false;
            } else {
                updateSearchStatus('未找到匹配项');
                elements.searchPrevBtn.disabled = true;
                elements.searchNextBtn.disabled = true;
                currentSearchIndex = -1;
            }
        }

        function clearSearchHighlights() {
            const highlightedElements = elements.messagesContainer.querySelectorAll('pre[data-original]');
            highlightedElements.forEach(element => {
                element.innerHTML = element.getAttribute('data-original');
                element.removeAttribute('data-original');
            });

            const currentHighlights = elements.messagesContainer.querySelectorAll('.search-current');
            currentHighlights.forEach(element => {
                element.className = 'search-highlight';
            });
        }

        function highlightCurrentMatch() {
            if (currentSearchIndex >= 0 && currentSearchIndex < searchMatches.length) {
                const currentHighlights = elements.messagesContainer.querySelectorAll('.search-current');
                currentHighlights.forEach(element => {
                    element.className = 'search-highlight';
                });

                const currentMatch = searchMatches[currentSearchIndex];
                const highlights = currentMatch.querySelectorAll('.search-highlight');
                if (highlights.length > 0) {
                    highlights[0].className = 'search-current';
                }

                // 🎯 搜索滚动：始终可用，不受自动滚动设置影响
                scrollToSearchResult(currentMatch);

                updateSearchStatus(`${currentSearchIndex + 1}/${searchMatches.length}`);
            }
        }

        function updateSearchStatus(text) {
            elements.searchStatus.textContent = text;
        }

        function searchPrevious() {
            if (searchMatches.length > 0) {
                currentSearchIndex = (currentSearchIndex - 1 + searchMatches.length) % searchMatches.length;
                highlightCurrentMatch();
            }
        }

        function searchNext() {
            if (searchMatches.length > 0) {
                currentSearchIndex = (currentSearchIndex + 1) % searchMatches.length;
                highlightCurrentMatch();
            }
        }

        function clearSearch() {
            elements.searchInput.value = '';
            clearSearchHighlights();
            searchMatches = [];
            currentSearchIndex = -1;
            lastSearchTerm = '';
            updateSearchStatus('');
            elements.searchPrevBtn.disabled = true;
            elements.searchNextBtn.disabled = true;
        }

        function updateConnectionStatus(status, className) {
            elements.connectionStatus.textContent = status;
            elements.connectionStatus.className = `connection-status px-3 py-1 rounded-full text-sm font-medium ${className}`;
        }

        function updateResponseType(type, className) {
            elements.responseType.textContent = type;
            elements.responseType.className = `px-3 py-1 rounded-full text-sm font-medium ${className}`;
        }

        function buildRequestConfig(url, dataText) {
            const config = {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer xxxxxx',
                    'X-Service-Key': 'jjmCeV9HHbnSsT',
                    'X-Service-Name': 'test',
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream, application/json, */*'
                },
                signal: abortController.signal
            };

            if (dataText.trim()) {
                try {
                    const data = JSON.parse(removeJsonComments(dataText));
                    config.body = JSON.stringify(data);
                } catch (error) {
                    throw new Error('JSON格式错误: ' + error.message);
                }
            } else {
                config.body = JSON.stringify({});
            }

            return { url, config };
        }

        function removeJsonComments(jsonString) {
            return jsonString
                .replace(/\/\*[\s\S]*?\*\//g, '')
                .replace(/\/\/.*$/gm, '');
        }

        function isSSEResponse(response) {
            const contentType = response.headers.get('content-type') || '';
            return contentType.includes('text/event-stream');
        }

        function parseSSEData(chunk) {
            const lines = chunk.split('\n');
            const events = [];
            let currentEvent = {};

            for (const line of lines) {
                if (line.trim() === '') {
                    if (currentEvent.data !== undefined) {
                        events.push(currentEvent);
                        currentEvent = {};
                    }
                } else if (line.startsWith('data: ')) {
                    currentEvent.data = line.substring(6);
                } else if (line.startsWith('event: ')) {
                    currentEvent.event = line.substring(7);
                } else if (line.startsWith('id: ')) {
                    currentEvent.id = line.substring(4);
                } else if (line.startsWith('retry: ')) {
                    currentEvent.retry = parseInt(line.substring(7));
                }
            }

            return events;
        }

        function addMessage(data, type = 'data', eventType = null) {
            messageCount++;

            const messageDiv = document.createElement('div');
            const borderColor = type === 'system' ? 'border-gray-400' :
                type === 'http' ? 'border-green-400' :
                    type === 'debug' ? 'border-yellow-400' : 'border-blue-400';
            messageDiv.className = `message-fade-in mb-3 p-3 border-l-4 ${borderColor} bg-white rounded shadow-sm`;

            const timestamp = new Date().toLocaleTimeString();

            let content;
            try {
                const parsed = JSON.parse(data);
                content = JSON.stringify(parsed, null, 2);
            } catch {
                content = data;
            }

            const eventLabel = eventType ? `${type.toUpperCase()} (${eventType})` : type.toUpperCase();

            const colorMap = {
                'http': 'text-green-600 bg-green-100',
                'debug': 'text-yellow-600 bg-yellow-100',
                'system': 'text-gray-600 bg-gray-100',
                'data': 'text-blue-600 bg-blue-100'
            };

            messageDiv.innerHTML = `
                <div class="flex justify-between items-start mb-2">
                    <span class="text-xs font-medium ${colorMap[type] || 'text-blue-600 bg-blue-100'} px-2 py-1 rounded">${eventLabel}</span>
                    <span class="text-xs text-gray-500">${timestamp}</span>
                </div>
                <pre class="text-sm text-gray-700 whitespace-pre-wrap break-words font-mono bg-gray-50 p-2 rounded">${content}</pre>
            `;

            // 清空欢迎消息
            if (elements.messagesContainer.children.length === 1 &&
                elements.messagesContainer.children[0].textContent.includes('等待接收数据')) {
                elements.messagesContainer.innerHTML = '';
            }

            elements.messagesContainer.appendChild(messageDiv);

            // 更新统计
            elements.totalMessages.textContent = messageCount;
            elements.lastMessageTime.textContent = timestamp;

            // 🎯 新消息添加后，尝试自动滚动到底部（只受 autoScrollDisabled 控制）
            setTimeout(scrollToBottom, 10);

            // 如果有搜索词，重新执行搜索高亮
            if (elements.searchInput.value.trim()) {
                performSearch(true);
            }
        }

        function addErrorMessage(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'message-fade-in mb-3 p-3 border-l-4 border-red-400 bg-red-50 rounded shadow-sm';
            const timestamp = new Date().toLocaleTimeString();

            errorDiv.innerHTML = `
                <div class="flex justify-between items-start mb-2">
                    <span class="text-xs font-medium text-red-600 bg-red-100 px-2 py-1 rounded">ERROR</span>
                    <span class="text-xs text-gray-500">${timestamp}</span>
                </div>
                <div class="text-sm text-red-700">${message}</div>
            `;

            elements.messagesContainer.appendChild(errorDiv);
            setTimeout(scrollToBottom, 10);

            if (elements.searchInput.value.trim()) {
                performSearch(true);
            }
        }

        function updateConnectionTime() {
            if (connectionStartTime) {
                const elapsed = Math.floor((Date.now() - connectionStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                elements.connectionTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        async function handleHttpResponse(response, requestUrl, dataText) {
            updateResponseType('HTTP 响应', 'bg-green-100 text-green-600');

            try {
                const text = await response.text();
                addMessage(`请求完成: ${requestUrl}`, 'system');
                if (dataText.trim()) {
                    addMessage(`请求体: ${dataText}`, 'system');
                }
                addMessage(`状态码: ${response.status} ${response.statusText}`, 'debug');

                const contentType = response.headers.get('content-type') || 'unknown';
                const contentLength = response.headers.get('content-length') || 'unknown';
                addMessage(`Content-Type: ${contentType}`, 'debug');
                addMessage(`Content-Length: ${contentLength}`, 'debug');

                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                addMessage(JSON.stringify(headers, null, 2), 'debug', 'All Response Headers');

                if (text) {
                    addMessage(text, 'http', 'Response Body');
                } else {
                    addMessage('响应体为空', 'debug');
                }

                updateConnectionStatus('请求完成', 'bg-green-100 text-green-600');

            } catch (error) {
                addErrorMessage('读取HTTP响应失败: ' + error.message);
                updateConnectionStatus('请求失败', 'bg-red-100 text-red-600');
            } finally {
                elements.connectBtn.disabled = false;
                elements.disconnectBtn.disabled = true;

                if (connectionTimer) {
                    clearInterval(connectionTimer);
                    connectionTimer = null;
                }
            }
        }

        async function handleSSEResponse(response, requestUrl, dataText) {
            updateResponseType('SSE 流', 'bg-blue-100 text-blue-600');

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';
            let dataReceived = false;

            addMessage(`开始SSE连接: ${requestUrl}`, 'system');
            if (dataText.trim()) {
                addMessage(`请求体: ${dataText}`, 'system');
            }

            const contentType = response.headers.get('content-type') || 'unknown';
            addMessage(`Content-Type: ${contentType}`, 'debug');

            try {
                while (true) {
                    const { value, done } = await reader.read();

                    if (done) {
                        if (dataReceived) {
                            addMessage('SSE连接已正常关闭', 'system');
                        } else {
                            addMessage('连接关闭 - 未接收到任何SSE数据', 'debug');
                            addMessage('这可能是一个普通HTTP响应而非SSE流', 'debug');
                        }
                        break;
                    }

                    dataReceived = true;
                    const chunk = decoder.decode(value, { stream: true });
                    addMessage(`${chunk}`, 'debug', 'Raw Data');

                    buffer += chunk;

                    const events = parseSSEData(buffer);

                    for (const event of events) {
                        if (event.data !== undefined) {
                            addMessage(event.data, 'data', event.event);
                        }
                    }

                    const lastNewlineIndex = buffer.lastIndexOf('\n\n');
                    if (lastNewlineIndex !== -1) {
                        buffer = buffer.substring(lastNewlineIndex + 2);
                    }
                }
            } catch (error) {
                if (error.name !== 'AbortError') {
                    addErrorMessage('读取SSE数据流时发生错误: ' + error.message);
                }
            } finally {
                reader.releaseLock();
                elements.connectBtn.disabled = false;
                elements.disconnectBtn.disabled = true;

                if (connectionTimer) {
                    clearInterval(connectionTimer);
                    connectionTimer = null;
                }
            }
        }

        async function connect() {
            const url = elements.sseUrl.value.trim();
            const dataText = elements.requestData.value.trim();

            if (!url) {
                alert('请输入服务地址');
                return;
            }

            try {
                abortController = new AbortController();
                const { url: requestUrl, config } = buildRequestConfig(url, dataText);

                updateConnectionStatus('连接中...', 'bg-yellow-100 text-yellow-600');
                updateResponseType('检测中...', 'bg-yellow-100 text-yellow-600');

                addMessage(`正在发送请求到: ${requestUrl}`, 'debug');
                addMessage(`请求方法: POST`, 'debug');

                const response = await fetch(requestUrl, config);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                updateConnectionStatus('已连接', 'bg-green-100 text-green-600');
                elements.connectBtn.disabled = true;
                elements.disconnectBtn.disabled = false;

                connectionStartTime = Date.now();
                connectionTimer = setInterval(updateConnectionTime, 1000);

                const contentType = response.headers.get('content-type') || '';
                addMessage(`检测到Content-Type: ${contentType}`, 'debug');

                if (isSSEResponse(response)) {
                    addMessage('检测为SSE响应，切换到流模式', 'debug');
                    await handleSSEResponse(response, requestUrl, dataText);
                } else {
                    addMessage('检测为普通HTTP响应，切换到一次性读取模式', 'debug');
                    await handleHttpResponse(response, requestUrl, dataText);
                }

            } catch (error) {
                if (error.name !== 'AbortError') {
                    addErrorMessage('请求失败: ' + error.message);
                    updateConnectionStatus('请求失败', 'bg-red-100 text-red-600');
                    updateResponseType('失败', 'bg-red-100 text-red-600');
                }

                elements.connectBtn.disabled = false;
                elements.disconnectBtn.disabled = true;

                if (connectionTimer) {
                    clearInterval(connectionTimer);
                    connectionTimer = null;
                }
            }
        }

        function disconnect() {
            if (abortController) {
                abortController.abort();
                abortController = null;
            }

            updateConnectionStatus('未连接', 'bg-gray-100 text-gray-600');
            updateResponseType('未知', 'bg-gray-100 text-gray-600');
            elements.connectBtn.disabled = false;
            elements.disconnectBtn.disabled = true;

            if (connectionTimer) {
                clearInterval(connectionTimer);
                connectionTimer = null;
            }

            addMessage('连接已断开', 'system');
        }

        function clearMessages() {
            elements.messagesContainer.innerHTML = `
                <div class="text-center text-gray-500 py-8">
                    <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                    </svg>
                    <p>等待接收数据...</p>
                    <p class="text-sm">支持 SSE 流式响应和普通 HTTP 响应</p>
                </div>
            `;

            messageCount = 0;
            elements.totalMessages.textContent = '0';
            elements.lastMessageTime.textContent = '--:--';

            // 🎯 清空消息时重置滚动状态
            autoScrollDisabled = false;
            isSystemScrolling = false;
            updateScrollStatus();

            clearSearch();
        }

        function formatJson() {
            const textarea = elements.requestData;
            const text = textarea.value.trim();

            if (!text) return;

            try {
                const parsed = JSON.parse(removeJsonComments(text));
                const formatted = JSON.stringify(parsed, null, 2);
                textarea.value = formatted;

                const btn = document.getElementById('formatJsonBtn');
                const originalText = btn.textContent;
                btn.textContent = '✓ 完成';
                btn.className = btn.className.replace('bg-blue-500 hover:bg-blue-600', 'bg-green-500');

                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.className = btn.className.replace('bg-green-500', 'bg-blue-500 hover:bg-blue-600');
                }, 1000);

            } catch (error) {
                const btn = document.getElementById('formatJsonBtn');
                const originalText = btn.textContent;
                btn.textContent = '✗ 错误';
                btn.className = btn.className.replace('bg-blue-500 hover:bg-blue-600', 'bg-red-500');

                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.className = btn.className.replace('bg-red-500', 'bg-blue-500 hover:bg-blue-600');
                }, 1500);
            }
        }

        function initEventListeners() {
            elements.connectBtn.addEventListener('click', connect);
            elements.disconnectBtn.addEventListener('click', disconnect);
            elements.clearDataIconBtn.addEventListener('click', clearMessages);
            document.getElementById('formatJsonBtn').addEventListener('click', formatJson);

            // 搜索相关事件
            elements.searchInput.addEventListener('input', () => performSearch(false));
            elements.searchInput.addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    if (e.shiftKey) {
                        searchPrevious();
                    } else {
                        searchNext();
                    }
                }
            });
            elements.searchPrevBtn.addEventListener('click', searchPrevious);
            elements.searchNextBtn.addEventListener('click', searchNext);

            elements.sseUrl.addEventListener('keypress', function (e) {
                if (e.key === 'Enter' && !elements.connectBtn.disabled) {
                    connect();
                }
            });

            // 🎯 监听滚动事件 - 只禁用新消息自动滚动，不影响搜索功能
            elements.messagesContainer.addEventListener('scroll', handleScroll);

            window.addEventListener('beforeunload', function () {
                if (abortController) {
                    abortController.abort();
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function () {
            initEventListeners();
            updateScrollStatus();
            console.log('SSE客户端已初始化 - 搜索定位修复版');
        });
    </script>
</body>

</html>